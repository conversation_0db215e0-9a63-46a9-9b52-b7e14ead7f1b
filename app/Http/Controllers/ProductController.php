<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Product;
use PDF;

class ProductController extends Controller
{
    /**
     * Instantiate a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function index()
    {
        return view('qr-reader');
    }

    public function subQuantity(Request $request)
    {
        $product = Product::where('sku', $request->input('sku'))->first();
        $product->quantity = $product->quantity - intval($request->input('subQuantity'));
        $product->save();
        return "OK";
    }

    public function downloadPDF(Product $product)
    {
        $pdf = PDF::loadView('pdf.product-label', compact('product'))->setPaper('a4', 'portrait');
        return $pdf->download('Product Label - ' . $product->sku . '.pdf');
    }
}

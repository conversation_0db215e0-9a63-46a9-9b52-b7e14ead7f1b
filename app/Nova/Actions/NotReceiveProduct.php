<?php

namespace App\Nova\Actions;

use App\Order;
use App\Product;
use App\OrderTransaction;
use Illuminate\Bus\Queueable;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Actions\Action;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class NotReceiveProduct extends Action
{
    public $name = 'Not Receive Product';

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $pivots)
    {
        foreach ($pivots as $pivot) {
            if ($pivot->ordered_quantity - ($pivot->received_quantity + $fields->quantity) >= 0) {
                $pivot->not_received_quantity += $fields->quantity;
                $pivot->save();
            }
            else {
                return Action::danger('Inserted number makes quantity below zero.');
            }

        }
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [
            Number::make('Quantity', 'quantity')->min(0)->max(10000)->step(1)->rules('required'),
        ];
    }
}

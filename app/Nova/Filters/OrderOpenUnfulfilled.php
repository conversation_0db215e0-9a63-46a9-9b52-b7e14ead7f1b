<?php

namespace App\Nova\Filters;

use Illuminate\Http\Request;
use Lara<PERSON>\Nova\Filters\Filter;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderOpenUnfulfilled extends Filter
{
    /**
     * The filter's component.
     *
     * @var string
     */
    public $component = 'select-filter';

    /**
     * Apply the filter to the given query.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  mixed  $value
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function apply(Request $request, $query, $value)
    {
        if ($value === 'open') {
            return $query->where('closed', 0);
        }
        elseif ($value === 'unfulfilled') {
            return $query;
        }
        else {
            return $query;
        }
    }

    /**
     * Get the filter's available options.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function options(Request $request)
    {
        return [
            'Open' => 'open',
            'Unfulfilled' => 'unfulfilled',
        ];
    }
}

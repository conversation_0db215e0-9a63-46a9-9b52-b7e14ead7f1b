<?php

namespace App\Nova\Lenses;

use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\ID;
use <PERSON>vel\Nova\Http\Requests\LensRequest;
use <PERSON>vel\Nova\Lenses\Lens;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON>vel\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\Boolean;

class OutOfStock extends Lens
{
    /**
     * Get the query builder / paginator for the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\LensRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return mixed
     */
    public static function query(LensRequest $request, $query)
    {
        return $request->withOrdering($request->withFilters(
            $query->where('quantity', '<', 1)
        ));
    }

    /**
     * Get the fields available to the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),
            Boolean::make('stock', function () {
                if (($this->quantity) < 1) {
                    return false;
                }
                else {
                    return true;
                }
            })->sortable(),
            Text::make('sku')->sortable(),
            Text::make('code')->sortable(),
            Number::make('grain')->min(1)->max(10000)->step(1)->sortable(),
            Number::make('height')->min(1)->max(10000)->step(1)->sortable(),
            Currency::make('price')->currency('EUR')->nullable()->sortable(),
            Number::make('quantity')->max(100000)->step(1)->sortable(),
        ];
    }

    /**
     * Get the cards available on the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available on the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return parent::actions($request);
    }

    /**
     * Get the URI key for the lens.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'out-of-stock';
    }
}

<?php

namespace App\Console\Commands;
use App\User;

use Illuminate\Console\Command;

class AssignRoleToUser extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'role:assign {user} {role}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Assign a given role to a specified user.';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $user = User::where('email',$this->argument('user'))->first();
        $user->assignRole($this->argument('role'));
        $this->info('Role assigned to user.');
    }
}

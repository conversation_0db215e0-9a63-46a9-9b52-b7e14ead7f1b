<?php

namespace Database\Factories;

use App\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductFactory extends Factory {
    protected $model = Product::class;

    public function definition() {
        $code = $this->faker->randomElement(["AAA", "BBB", "CCC", "DDD", "EEE", "FFF", "GGG", "HHH"]);
        $grain = $this->faker->randomElement([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200]);
        $height = $this->faker->randomElement([10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120, 130, 140, 150, 160, 170, 180, 190, 200]);
        $randomn = $this->faker->numberBetween(2, 10000);
        $sku = "$code.$grain.$height.$randomn";
        return [
            'sku' => $sku,
            'code' => $code,
            'grain' => $grain,
            'height' => $height,
            'price' => $this->faker->randomFloat(2, 10, 500),
            'quantity' => $this->faker->numberBetween(2, 100),
            'diameter' => $this->faker->numberBetween(2, 100)
        ];
    }
}

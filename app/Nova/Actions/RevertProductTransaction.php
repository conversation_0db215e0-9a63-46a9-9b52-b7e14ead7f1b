<?php

namespace App\Nova\Actions;

use App\Product;
use Illuminate\Bus\Queueable;
use Lara<PERSON>\Nova\Actions\Action;
use Illuminate\Support\Collection;
use <PERSON>vel\Nova\Fields\ActionFields;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;

class RevertProductTransaction extends Action
{
    use InteractsWithQueue, Queueable;

    /**
     * Perform the action on the given models.
     *
     * @param  \Laravel\Nova\Fields\ActionFields  $fields
     * @param  \Illuminate\Support\Collection  $models
     * @return mixed
     */
    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            // Assuming you have a product_id and quantity_change fields in your ProductTransaction model
            $product = Product::find($model->product_id);

            if ($model->type === 'positive') {
                $product->quantity -= $model->quantity_change;
            } else {
                $product->quantity += $model->quantity_change;
            }

            $product->save();
        }

        return Action::message('The product transaction has been reverted!');
    }

    /**
     * Get the fields available on the action.
     *
     * @return array
     */
    public function fields()
    {
        return [];
    }
}

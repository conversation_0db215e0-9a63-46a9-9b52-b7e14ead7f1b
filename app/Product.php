<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;

class Product extends Model
{
    use LogsActivity;
    use HasFactory;
    use SoftDeletes;

    protected $casts = [
        'date' => 'date'
    ];

    protected static $logAttributes = ['sku', 'code', 'grain', 'height', 'price', 'quantity'];

    public function orders()
    {
        return $this->belongsToMany(Order::class)->withPivot('ordered_quantity', 'received_quantity', 'price', 'not_received_quantity');
    }

    public function productTransactions()
    {
        return $this->hasMany(ProductTransaction::class);
    }

    public function getLowStockAlertAttribute(): bool
    {
        if ($this->quantity < $this->low_stock_threshold) {
            return false;
        }
        return true;
    }
}

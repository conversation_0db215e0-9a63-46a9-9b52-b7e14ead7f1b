<?php

namespace App;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;

class OrderProduct extends Model
{
    use LogsActivity;

    protected static $logAttributes = ['ordered_quantity', 'received_quantity', 'not_received_quantity'];

    public function order()
    {
        return $this->belongsToOne(Order::class);
    }

    public function product()
    {
        return $this->hasOne(Product::class);
    }
}

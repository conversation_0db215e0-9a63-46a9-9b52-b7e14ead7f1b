<?php

namespace App\Nova\Actions;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Number;
use App\Product;

class ZeroQuantity extends Action
{
    use InteractsWithQueue, Queueable;

    public $name = 'Zero Quantity';

    public function handle(ActionFields $fields, Collection $models)
    {
        foreach ($models as $model) {
            $product = Product::withTrashed()->findOrFail($model->id);
            $product->quantity = 0;
            $product->save();
        }
    }

    public function fields()
    {
        return [];
    }
}

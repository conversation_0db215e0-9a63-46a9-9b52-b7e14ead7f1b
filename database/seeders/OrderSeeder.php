<?php

namespace Database\Seeders;

use App\Order;
use App\Product;
use Illuminate\Database\Seeder;

class OrderSeeder extends Seeder
{
    public function run()
    {
        Order::factory()
                ->count(5)
                ->hasAttached(
                    Product::all()->random(20),
                    ['ordered_quantity' => rand(50, 100), 'received_quantity' => rand(0, 49), 'price' => rand(0, 49)]
                )
                ->create();
    }
}

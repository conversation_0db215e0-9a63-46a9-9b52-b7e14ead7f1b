<?php

return [

    // If true, hide all sidebar headlines
    'hide_all_sidebar_headlines' => false,

    // List of sidebar headlines to hide (Ex.: ["Other"])
    'hidden_sidebar_headlines' => [],

    // If true, the resource tables actions will be always visible (sticky)
    'resource_tables_sticky_actions' => false,

    // If true, the resource tables actions will be always visible (sticky) on mobile
    'resource_tables_sticky_actions_on_mobile' => false,

    // If true, hides the "Update & Continue Editing" button on "Update" forms
    'hide_update_and_continue_editing_button' => false,

    // If true, hides the "Update & Continue Editing" button on "Update" forms (mobile only)
    'hide_update_and_continue_editing_button_on_mobile' => false,

    // If true, the sidebar will stay fixed on desktop view
    'fixed_sidebar' => false,

];

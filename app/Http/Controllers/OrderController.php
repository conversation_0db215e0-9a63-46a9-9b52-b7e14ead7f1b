<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Order;
use PDF;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
    }

    public function downloadPDF(Order $order, Request $request)
    {
        //return view('pdf.order-bill', compact('order'));
        $withPrice = $request->query('withPrice', false);
        $pdf = PDF::loadView('pdf.order-bill', compact('order', 'withPrice'))->setPaper('a4', 'landscape');
        return $pdf->download('Order ' . $order->number . '.pdf');
    }
}

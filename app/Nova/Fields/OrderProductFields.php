<?php

namespace App\Nova\Fields;

use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\Boolean;

class OrderProductFields
{
    public function __invoke()
    {
        return [
            Number::make(__('Ordered Quantity'), 'ordered_quantity')->rules('required'),
            Number::make(__('Received Quantity'), 'received_quantity')->exceptOnForms(),
            Number::make(__('Not Received Quantity'), 'not_received_quantity')->exceptOnForms(),
            Number::make(__('Missing Quantity'), function ($pivotData) {
                return $pivotData->ordered_quantity - ($pivotData->received_quantity + $pivotData->not_received_quantity);
            }),
            Currency::make(__('Price'), 'price')->currency('EUR')->nullable(),
            Boolean::make(__('Product Fulfilled'), function ($pivotData) {
                return !($pivotData->ordered_quantity > $pivotData->received_quantity + $pivotData->not_received_quantity);
            }),
        ];
    }
}

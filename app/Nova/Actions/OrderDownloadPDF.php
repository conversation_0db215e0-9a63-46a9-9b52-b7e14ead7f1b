<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Boolean;
use App\Product;
use App\OrderTransaction;
use App\Order;

class OrderDownloadPDF extends Action
{
    public $name = 'Download as PDF';
    public $onlyOnDetail = true;

    public function handle(ActionFields $fields, Collection $orders)
    {
        $withPrice = $fields->withPrice;
        $order = $orders->first();
        return Action::redirect(url("/downloadOrderPDF/{$order->id}?withPrice={$withPrice}"));
    }

    public function fields()
    {
        return [
            Boolean::make('With price', 'withPrice')->default(function ($request) {
                return false;
            })
        ];
    }
}

<?php

namespace App\Nova\Lenses;

use App\Nova\Actions\OrderProductActions;
use App\Nova\Fields\OrderProductFields;
use Illuminate\Http\Request;
use <PERSON>vel\Nova\Fields\BelongsToMany;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\HasMany;
use Laravel\Nova\Fields\ID;
use Laravel\Nova\Fields\Number;
use Laravel\Nova\Fields\Text;
use Laravel\Nova\Http\Requests\LensRequest;
use Laravel\Nova\Lenses\Lens;

class ClosedOrders extends Lens
{
    /**
     * Get the query builder / paginator for the lens.
     *
     * @param  \Laravel\Nova\Http\Requests\LensRequest  $request
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return mixed
     */
    public static function query(LensRequest $request, $query)
    {
        return $request->withOrdering($request->withFilters(
            $query->where('closed', 1)
        ));
    }

    /**
     * Get the fields available to the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make(__('ID'), 'id')->sortable(),
            Text::make(__('Number'), 'number')->sortable(),
            Text::make(__('Supplier'), 'supplier')->sortable(),
            Date::make(__('Date'), 'date')->sortable(),
            Boolean::make(__('Order Closed'), 'closed')->hideWhenCreating(),
            Boolean::make(__('Order Fulfilled'), function ($order) {
                foreach($order->products as $product) {
                    if ($product->pivot->ordered_quantity > $product->pivot->received_quantity)
                        return false;
                }
                return true;
            })
        ];
    }

    /**
     * Get the cards available on the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the actions available on the lens.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return parent::actions($request);
    }

    /**
     * Get the URI key for the lens.
     *
     * @return string
     */
    public function uriKey()
    {
        return 'closed-orders';
    }
}

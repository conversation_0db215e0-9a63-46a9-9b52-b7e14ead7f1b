<?php

namespace App\Observers;

use App\Product;
use App\ProductTransaction;

class ProductObserver
{
    /**
     * Handle the Product "created" event.
     *
     * @param  \App\Product  $product
     * @return void
     */
    public function created(Product $product)
    {
        //
    }

    /**
     * Handle the Product "updated" event.
     *
     * @param  \App\Product  $product
     * @return void
     */
    public function updated(Product $product)
    {
        // Verifica se 'quantity' è stato modificato
        if ($product->isDirty('quantity')) {
            // Ottieni il valore precedente e il nuovo valore
            $oldQuantity = $product->getOriginal('quantity');
            $newQuantity = $product->quantity;

            // Calcola la differenza
            $difference = $newQuantity - $oldQuantity;

            // Determina il tipo di transazione
            $type = $difference > 0 ? 'positive' : 'negative';

            // Crea una nuova transazione
            ProductTransaction::create([
                'product_id' => $product->id,
                'quantity_change' => abs($difference),
                'type' => $type,
            ]);
        }
    }

    /**
     * Handle the Product "deleted" event.
     *
     * @param  \App\Product  $product
     * @return void
     */
    public function deleted(Product $product)
    {
        //
    }

    /**
     * Handle the Product "restored" event.
     *
     * @param  \App\Product  $product
     * @return void
     */
    public function restored(Product $product)
    {
        //
    }

    /**
     * Handle the Product "force deleted" event.
     *
     * @param  \App\Product  $product
     * @return void
     */
    public function forceDeleted(Product $product)
    {
        //
    }
}

<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON>vel\Nova\Fields\Number;
use App\Product;
use App\OrderTransaction;
use App\Order;

class ReceiveProduct extends Action
{
    public $name = 'Receive Product';

    public function handle(ActionFields $fields, Collection $pivots)
    {
        foreach ($pivots as $pivot) {
            $product = Product::withTrashed()->findOrFail($pivot->product_id);
            $order = Order::findOrFail($pivot->order_id);

            $product->quantity += $fields->quantity;
            $product->save();

            $pivot->received_quantity += $fields->quantity;
            $pivot->save();

            $transaction = new OrderTransaction;
            $transaction->product_id = $product->id;
            $transaction->order_id = $order->id;
            $transaction->quantity_change = $fields->quantity;
            $transaction->save();
        }
    }

    public function fields()
    {
        return [
            Number::make('Quantity', 'quantity')->min(0)->max(10000)->step(1)->rules('required'),
        ];
    }
}

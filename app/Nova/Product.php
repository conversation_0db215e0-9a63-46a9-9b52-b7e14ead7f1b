<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Text;
use <PERSON><PERSON>\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use <PERSON><PERSON>\Nova\Fields\Currency;
use <PERSON><PERSON>\Nova\Fields\BelongsToMany;
use App\Nova\Fields\OrderProductFields;
use App\Nova\Actions\OrderProductActions;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use <PERSON>vel\Nova\Testing\Browser\Pages\Lens;
use <PERSON>vel\Nova\Http\Requests\ResourceIndexRequest;
use Maatwebsite\LaravelNovaExcel\Actions\DownloadExcel;

class Product extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Product::class;
    public static $group = 'Orders & Products';

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'sku';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'id', 'sku', 'code', 'grain', 'height', 'diameter'
    ];

    public static $perPageViaRelationship = 50;
    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),
            Boolean::make('stock', function () {
                if (($this->quantity) < 1) {
                    return false;
                }
                else {
                    return true;
                }
            })
                ->sortable()
                ->hideFromIndex(function ($request) {
                    return $request->viaRelationship();
                }),
            Boolean::make('low_stock_alert')
                ->readonly(),
            Number::make('low_stock_threshold')->max(100000)->step(1)->sortable()
                ->hideFromIndex(),
            Text::make('sku')->sortable(),
            Text::make('code')->sortable(),
            Number::make('grain')->min(0)->max(10000)->step(1)->sortable()
                ->hideFromIndex(function ($request) {
                    return $request->viaRelationship();
                }),
            Number::make('height')->min(0)->max(10000)->step(1)->sortable()
                ->hideFromIndex(function ($request) {
                    return $request->viaRelationship();
                }),
            Number::make('diameter')->min(0)->max(10000)->step(1)->nullable()->sortable()
                ->hideFromIndex(function ($request) {
                    return $request->viaRelationship();
                }),
            Currency::make('Price', 'price')->currency('EUR')->nullable()->sortable()
                ->hideFromIndex(function ($request) {
                    return $request->viaRelationship();
                }),
            Number::make('quantity')->max(100000)->step(1)->sortable()
                ->hideFromIndex(function ($request) {
                    return $request->viaRelationship();
                }),
            BelongsToMany::make(__('Orders'), 'orders')
                ->fields(new OrderProductFields)
                ->actions(new OrderProductActions),
            HasMany::make(__('Product Transactions'), 'productTransactions')
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [
            new Lenses\OutOfStock(static::newModel()),
            new Lenses\LowStock(static::newModel()),
        ];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new Actions\AddQuantity,
            new Actions\ZeroQuantity,
            new Actions\PrintLabel,
            (new DownloadExcel)->withHeadings(),
            new Actions\ProductDownloadActivityExcel,
        ];
    }
}

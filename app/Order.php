<?php

namespace App;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Traits\LogsActivity;

class Order extends Model
{
    use LogsActivity;
    use HasFactory;

    protected static $logAttributes = ['number', 'date', 'supplier'];

    public function products()
    {
        return $this->belongsToMany(Product::class)->withPivot('ordered_quantity', 'received_quantity', 'price', 'not_received_quantity');
    }

    public function transactions()
    {
        return $this->hasMany(OrderTransaction::class);
    }

    protected $casts = [
        'date' => 'date'
    ];
}

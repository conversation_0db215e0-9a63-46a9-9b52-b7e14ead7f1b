<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use App\Order;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class OrderDownloadExcel extends Action
{
    public $name = 'Download as Excel';

    function tableData(Collection $orders, $onlyNotFulfilled = false) {
        $rows = array();

        foreach ($orders as $order) {
            foreach ($order->products as $product) {
                $row = array();
                $row['product_sku'] = $product->sku;
                $row['product_code'] = $product->code;
                $row['product_grain'] = $product->grain;
                $row['order_number'] = $order->number;
                $row['order_date'] = $order->date->format('d/m/Y');
                $row['product_height'] = $product->height;
                $row['product_diameter'] = $product->diameter;
                $row['pivot_ordered_quantity'] = $product->pivot->ordered_quantity;
                $row['pivot_received_quantity'] = $product->pivot->received_quantity;
                $row['pivot_not_received_quantity'] = $product->pivot->not_received_quantity;
                $row['pivot_missing_quantity'] = $product->pivot->ordered_quantity - $product->pivot->received_quantity;
                $row['pivot_price'] = $product->pivot->price;
                if($onlyNotFulfilled === true){
                    if($product->pivot->received_quantity < $product->pivot->ordered_quantity){
                        array_push($rows, $row);
                    }
                }else{
                    array_push($rows, $row);
                }

            }
        }

        array_multisort(
            array_column($rows, 'product_code'), SORT_ASC,
            array_column($rows, 'product_grain'), SORT_ASC,
            array_column($rows, 'product_height'), SORT_ASC,
            array_column($rows, 'product_diameter'), SORT_ASC,
            $rows
        );

        // Add table headings manually
        $headings = array();
        $headings['product_sku'] = "ARTICOLO";
        $headings['product_code'] = "CODICE";
        $headings['product_grain'] = "P";
        $headings['order_number'] = "N ORD";
        $headings['order_date'] = "DATA ORD";
        $headings['product_height'] = "H";
        $headings['product_diameter'] = "DIAM";
        $headings['pivot_ordered_quantity'] = "M ORD";
        $headings['pivot_received_quantity'] = "M ARR";
        $headings['pivot_not_received_quantity'] = "STORNO";
        $headings['pivot_missing_quantity'] = "RESIDUO";
        $headings['pivot_price'] = "PREZZO";
        array_unshift($rows, $headings);
        return $rows;
    }

    public function handle(ActionFields $fields, Collection $orders)
    {
        $rows = $this->tableData($orders, $fields->onlyNotFulfilled);

        $withPrice = $fields->withPrice;
        $singleOrder = false;
        $orderDate = null;
        $orderNumber = null;
        if ($orders->count() == 1) {
            $singleOrder = true;
            $orderDate = $orders->first()->date;
            $orderNumber = $orders->first()->number;
        }
        for ($i=0; $i < count($rows); $i++) {
            $row = $rows[$i];
            unset($row['product_code']);
            if ($singleOrder) {
                unset($row['order_number']);
                unset($row['order_date']);
            }
            if (!$withPrice) {
                unset($row['pivot_price']);
            }
            $rows[$i] = $row;
        }
        // build the spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // add sheet heading
        // LOGO
        $drawing = new Drawing();
        $drawing->setDescription('Logo Giussani Abrasivi');
        $drawing->setPath(public_path('/logo-no-text.png'));
        $drawing->setCoordinates('A1');
        $drawing->setHeight(90);
        $drawing->setWorksheet($sheet);

        // COMPANY TEXT
        $htmlHelper = new \PhpOffice\PhpSpreadsheet\Helper\Html();
        $html = "<b>Sede legale ed amministrativa</b> <br>
        <font color='#73b072'><b>Via Reali, 1 - 20037 Paderno Dugnano (MI)</b></font> <br>
        Tel. 02/9183001 - 02/99043001-2 - Fax 02/9180107 <br>
        Internet: http://www.giussaniabrasivi.it <br>
        E-mail: <EMAIL> <br>
        PEC: <EMAIL> - Codice SDI: <b>A4707H7</b> <br>
        CAP. SOC. EURO 10.920,00 i.v. - R.E.A. MI 989274 <br>
        P.IVA IT 00762040962 - REG. IMP. MILANO E C.F. 03759600152";
        $richText = $htmlHelper->toRichTextObject($html);
        $sheet->getStyle('B1')->getAlignment()->setWrapText(true);
        $sheet->setCellValue('B1', $richText);
        $sheet->mergeCells('B1:G1');
        $sheet->getStyle('B1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        // add data from A5
        $sheet->fromArray($rows, NULL, 'A5');

        if ($singleOrder) {
            $sheet->setCellValue('A3', 'N ORD');
            $sheet->setCellValue('B3', $orderNumber);
            $sheet->setCellValue('C3', 'DATA');
            $sheet->setCellValue('D3', $orderDate->format('d/m/Y'));
            $sheet->getStyle('3:3')->getFont()->setBold(true);
        }
        // make it pretty
        // show gridlines
        // what is the name of the last table data cell?
        // 4 because of A5
        $lastDataCell = chr(ord('A') + count(array_keys($rows[0])) - 1) . (count($rows) + 4);
        $sheet->getStyle("A5:" . $lastDataCell)
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN)
            ->setColor(new Color('00000000'));
        // set 1st row height
        $sheet->getRowDimension('1')->setRowHeight(125);
        // heading table row bold
        $sheet->getStyle('5:5')->getFont()->setBold(true);
        // expand columns
        $sheet->getColumnDimension('A')->setAutoSize(true);
        // align all to the left
        $sheet->getStyle('A:Z')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $writer = new Xlsx($spreadsheet);
        ob_start();
        $writer->save('php://output');
        $content = ob_get_contents();
        ob_end_clean();

        $filename = 'order-export/' . ((string) Str::uuid()) . ".xlsx";
        Storage::disk('public')->put($filename, $content);
        return Action::download(Storage::url($filename), 'orders.xlsx');
    }

    public function fields()
    {
        return [
            Boolean::make('With price', 'withPrice')->default(function ($request) {
                return false;
            }),
            Boolean::make('Only not Fulfilled', 'onlyNotFulfilled')->default(function ($request) {
                return false;
            })
        ];
    }
}

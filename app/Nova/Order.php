<?php

namespace App\Nova;

use <PERSON><PERSON>\Nova\Fields\ID;
use Illuminate\Http\Request;
use <PERSON><PERSON>\Nova\Fields\Date;
use <PERSON>vel\Nova\Fields\Text;
use <PERSON>vel\Nova\Fields\Number;
use <PERSON><PERSON>\Nova\Fields\Select;
use <PERSON>vel\Nova\Fields\Boolean;
use <PERSON><PERSON>\Nova\Fields\HasMany;
use App\Nova\Actions\SetOrderClosed;
use App\Nova\Actions\OrderDownloadPDF;
use Laravel\Nova\Fields\BelongsToMany;
use App\Nova\Fields\OrderProductFields;
use App\Nova\Actions\OrderDownloadExcel;
use App\Nova\Actions\OrderProductActions;
use App\Nova\Filters\OrderOpenUnfulfilled;
use <PERSON>vel\Nova\Http\Requests\NovaRequest;
use Laravel\Nova\Testing\Browser\Pages\Lens;

class Order extends Resource
{
    /**
     * The model the resource corresponds to.
     *
     * @var string
     */
    public static $model = \App\Order::class;

    /**
     * The single value that should be used to represent the resource when being displayed.
     *
     * @var string
     */
    public static $title = 'number';
    public static $group = 'Orders & Products';

    /**
     * The columns that should be searched.
     *
     * @var array
     */
    public static $search = [
        'number',
        'supplier',
    ];

    /**
     * The pagination per-page options configured for this resource.
     *
     * @return array
     */
    public static $perPageOptions = [200, 200, 200];

    /**
     * Get the fields displayed by the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function fields(Request $request)
    {
        return [
            ID::make()->sortable(),
            Text::make(__('Number'), 'number')->sortable(),
            Text::make(__('Supplier'), 'supplier')->sortable(),
            Select::make(__('Supplier', 'supplier'))->options([
                'supplier1' => 'Supplier 1',
                'supplier2' => 'Supplier 2',
                'supplier3' => 'Supplier 3',
            ]),
            Date::make(__('Date'), 'date')->sortable(),
            Boolean::make(__('Order Closed'), 'closed')->hideWhenCreating(),
            Boolean::make(__('Order Fulfilled'), function ($order) {
                foreach($order->products as $product) {
                    if ($product->pivot->ordered_quantity > $product->pivot->received_quantity + $product->pivot->not_received_quantity)
                        return false;
                }
                return true;
            }),
            BelongsToMany::make(__('Products'), 'products')
                ->fields(new OrderProductFields)
                ->actions(new OrderProductActions)
                ->searchable(),
            HasMany::make(__('Order Transactions'), 'transactions')
        ];
    }

    /**
     * Get the cards available for the request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function cards(Request $request)
    {
        return [];
    }

    /**
     * Get the filters available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function filters(Request $request)
    {
        return [
            new OrderOpenUnfulfilled,
        ];
    }

    /**
     * Get the lenses available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function lenses(Request $request)
    {
        return [
            new Lenses\OpenOrders,
            new Lenses\ClosedOrders,
        ];
    }

    /**
     * Get the actions available for the resource.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function actions(Request $request)
    {
        return [
            new OrderDownloadExcel,
            new OrderDownloadPDF,
            new SetOrderClosed
        ];
    }
}

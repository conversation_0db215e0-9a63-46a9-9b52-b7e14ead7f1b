<?php

namespace Database\Factories;

use App\Order;
use Illuminate\Database\Eloquent\Factories\Factory;

class OrderFactory extends Factory {
    protected $model = Order::class;

    public function definition() {
        return [
            'number' => $this->faker->numberBetween(1, 1000),
            'date' => $this->faker->dateTime(),
            'supplier' => $this->faker->company,
        ];
    }
}

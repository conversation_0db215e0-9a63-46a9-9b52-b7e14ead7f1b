<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use <PERSON><PERSON>\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Number;
use App\Product;
use App\OrderTransaction;
use App\Order;

class SetProductReceived extends Action
{
    public $name = 'Set Product Received';

    public function handle(ActionFields $fields, Collection $pivots)
    {
        foreach ($pivots as $pivot) {
            $missingQuantity = $pivot->ordered_quantity - $pivot->received_quantity;
            if($missingQuantity > 0)
                $pivot->received_quantity = $pivot->ordered_quantity;
            $pivot->save();
        }
    }

    public function fields()
    {
        return [];
    }
}

<?php

namespace App\Nova\Actions;

use Illuminate\Support\Collection;
use Laravel\Nova\Actions\Action;
use Laravel\Nova\Fields\ActionFields;
use <PERSON><PERSON>\Nova\Fields\Boolean;
use <PERSON>vel\Nova\Fields\DateTime;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use App\Order;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

class ProductDownloadActivityExcel extends Action
{
    public $name = 'Download Activity as Excel';

    function tableData($resourceId, $from, $to) {

        $product = DB::table('products')->where('id', $resourceId)->first();
        $activities = DB::table('activity_log')
            ->where('subject_type', 'App\Product')
            ->whereBetween('created_at', [$from,$to])
            ->whereJsonContains('properties->attributes->sku', $product->sku)
            ->get();

        $rows = array();
        foreach ($activities as $activity) {
            $row = array();
            $row['product_sku'] = $product->sku;
            $row['log_name'] = $activity->log_name;
            $row['description'] = $activity->description;
            $row['causer_id'] = $activity->causer_id;
            $row['causer_type'] = $activity->causer_type;
            $row['created_at'] = $activity->created_at;
            array_push($rows, $row);
        }
        // Add table headings manually
        $headings = array();
        $headings['product_sku'] = "ARTICOLO";
        $headings['log_name'] = "LOG NAME";
        $headings['description'] = "DESCRIPTION";
        $headings['causer_id'] = "CAUSER ID";
        $headings['causer_type'] = "CAUSER TYPE";
        $headings['created_at'] = "CREATED AT";
        array_unshift($rows, $headings);

        return $rows;
    }

    public function handle(ActionFields $fields)
    {

        $rows = $this->tableData($_REQUEST['resources'], $fields['data_da'], $fields['data_a']);
        // build the spreadsheet
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // add sheet heading
        // LOGO
        $drawing = new Drawing();
        $drawing->setDescription('Logo Giussani Abrasivi');
        $drawing->setPath(public_path('/logo-no-text.png'));
        $drawing->setCoordinates('A1');
        $drawing->setHeight(90);
        $drawing->setWorksheet($sheet);

        // COMPANY TEXT
        $htmlHelper = new \PhpOffice\PhpSpreadsheet\Helper\Html();
        $html = "<b>Sede legale ed amministrativa</b> <br>
        <font color='#73b072'><b>Via Reali, 1 - 20037 Paderno Dugnano (MI)</b></font> <br>
        Tel. 02/9183001 - 02/99043001-2 - Fax 02/9180107 <br>
        Internet: http://www.giussaniabrasivi.it <br>
        E-mail: <EMAIL> <br>
        PEC: <EMAIL> - Codice SDI: <b>A4707H7</b> <br>
        CAP. SOC. EURO 10.920,00 i.v. - R.E.A. MI 989274 <br>
        P.IVA IT 00762040962 - REG. IMP. MILANO E C.F. 03759600152";
        $richText = $htmlHelper->toRichTextObject($html);
        $sheet->getStyle('B1')->getAlignment()->setWrapText(true);
        $sheet->setCellValue('B1', $richText);
        $sheet->mergeCells('B1:G1');
        $sheet->getStyle('B1')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        // add data from A5
        $sheet->fromArray($rows, NULL, 'A5');

        // make it pretty
        // show gridlines
        // what is the name of the last table data cell?
        // 4 because of A5
        $lastDataCell = chr(ord('A') + count(array_keys($rows[0])) - 1) . (count($rows) + 4);
        $sheet->getStyle("A5:" . $lastDataCell)
            ->getBorders()
            ->getAllBorders()
            ->setBorderStyle(Border::BORDER_THIN)
            ->setColor(new Color('00000000'));
        // set 1st row height
        $sheet->getRowDimension('1')->setRowHeight(125);
        // heading table row bold
        $sheet->getStyle('5:5')->getFont()->setBold(true);
        // expand columns
        $sheet->getColumnDimension('A')->setAutoSize(true);
        // align all to the left
        $sheet->getStyle('A:Z')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);

        $writer = new Xlsx($spreadsheet);
        ob_start();
        $writer->save('php://output');
        $content = ob_get_contents();
        ob_end_clean();

        $filename = 'order-export/' . ((string) Str::uuid()) . ".xlsx";
        Storage::disk('public')->put($filename, $content);
        return Action::download(Storage::url($filename), 'orders.xlsx');
    }

    public function fields()
    {
        return [
            DateTime::make(__('Da'), 'data_da')
                ->firstDayOfWeek(1)
                ->format('DD MMM YYYY | HH:mm')
                ->required(),
            DateTime::make(__('A'), 'data_a')
                ->firstDayOfWeek(1)
                ->format('DD MMM YYYY | HH:mm')
                ->required(),
        ];
    }
}
